#!/usr/bin/env python3
"""
GHL File Discrepancy Analysis
Analyzes the difference between old and new GHL files to understand sync issues
"""

import pandas as pd
import os

def analyze_ghl_files():
    """Analyze GHL files to understand the 2398 vs 2371 discrepancy"""
    
    print("🔍 GHL File Discrepancy Analysis")
    print("=" * 50)
    
    # File paths
    old_file = "quickfix - ghl old.csv"
    new_file = "quickfix - ghl.xlsx"
    
    # Check if files exist
    if not os.path.exists(old_file):
        print(f"❌ Old file not found: {old_file}")
        return
    
    if not os.path.exists(new_file):
        print(f"❌ New file not found: {new_file}")
        return
    
    print(f"📂 Loading files...")
    
    # Load files
    try:
        old_data = pd.read_csv(old_file)
        print(f"✅ Loaded old CSV: {len(old_data):,} records")
    except Exception as e:
        print(f"❌ Error loading old file: {e}")
        return
    
    try:
        new_data = pd.read_excel(new_file)
        print(f"✅ Loaded new Excel: {len(new_data):,} records")
    except Exception as e:
        print(f"❌ Error loading new file: {e}")
        return
    
    print("\n📊 Basic Comparison:")
    print(f"   Old file records: {len(old_data):,}")
    print(f"   New file records: {len(new_data):,}")
    print(f"   Difference: {len(new_data) - len(old_data):,} records")
    print(f"   Airtable records: 2,371")
    print(f"   Missing from Airtable: {len(new_data) - 2371:,} records")
    
    # Column comparison
    print("\n📋 Column Analysis:")
    old_cols = set(old_data.columns)
    new_cols = set(new_data.columns)
    
    print(f"   Old file columns: {len(old_cols)}")
    print(f"   New file columns: {len(new_cols)}")
    
    if old_cols != new_cols:
        missing_in_new = old_cols - new_cols
        added_in_new = new_cols - old_cols
        
        if missing_in_new:
            print(f"   ⚠️ Missing in new: {list(missing_in_new)}")
        if added_in_new:
            print(f"   ✅ Added in new: {list(added_in_new)}")
    else:
        print(f"   ✅ Columns are identical")
    
    # Check Contact ID consistency
    if 'Contact ID' in old_data.columns and 'Contact ID' in new_data.columns:
        print("\n🔍 Contact ID Analysis:")
        
        # Get unique Contact IDs
        old_contact_ids = set(old_data['Contact ID'].astype(str))
        new_contact_ids = set(new_data['Contact ID'].astype(str))
        
        print(f"   Unique Contact IDs in old file: {len(old_contact_ids):,}")
        print(f"   Unique Contact IDs in new file: {len(new_contact_ids):,}")
        
        # Check overlap
        common_ids = old_contact_ids.intersection(new_contact_ids)
        only_in_old = old_contact_ids - new_contact_ids
        only_in_new = new_contact_ids - old_contact_ids
        
        print(f"   Common Contact IDs: {len(common_ids):,}")
        print(f"   Only in old file: {len(only_in_old):,}")
        print(f"   Only in new file: {len(only_in_new):,}")
        
        if len(only_in_old) > 0:
            print(f"   ⚠️ {len(only_in_old)} Contact IDs from old file missing in new!")
            if len(only_in_old) <= 10:
                print(f"   Missing IDs: {list(only_in_old)}")
        
        # Check for duplicates
        old_duplicates = old_data['Contact ID'].duplicated().sum()
        new_duplicates = new_data['Contact ID'].duplicated().sum()
        
        print(f"\n🔍 Duplicate Analysis:")
        print(f"   Duplicates in old file: {old_duplicates}")
        print(f"   Duplicates in new file: {new_duplicates}")
        
        if new_duplicates > 0:
            print(f"   ⚠️ Found {new_duplicates} duplicate Contact IDs in new file!")
            # Show some duplicate IDs
            duplicate_ids = new_data[new_data['Contact ID'].duplicated(keep=False)]['Contact ID'].unique()
            print(f"   Sample duplicate IDs: {list(duplicate_ids[:5])}")
    
    # Check first N records consistency (where N = length of old file)
    if len(old_data) > 0 and len(new_data) >= len(old_data):
        print(f"\n🔍 First {len(old_data)} Records Consistency:")
        
        # Compare first portion of new file with old file
        new_first_portion = new_data.head(len(old_data))
        
        if 'Contact ID' in old_data.columns:
            old_first_ids = set(old_data['Contact ID'].astype(str))
            new_first_ids = set(new_first_portion['Contact ID'].astype(str))
            
            matching = old_first_ids.intersection(new_first_ids)
            print(f"   Matching Contact IDs: {len(matching):,} / {len(old_first_ids):,}")
            print(f"   Match percentage: {len(matching)/len(old_first_ids)*100:.1f}%")
            
            if len(matching) != len(old_first_ids):
                print(f"   ⚠️ First {len(old_data)} records are NOT identical!")
                print(f"   This explains the Airtable discrepancy!")
    
    # Analyze date ranges
    if 'Date Created' in new_data.columns:
        print(f"\n📅 Date Analysis:")
        try:
            dates = pd.to_datetime(new_data['Date Created'], errors='coerce')
            valid_dates = dates.dropna()
            
            if not valid_dates.empty:
                print(f"   Date range: {valid_dates.min()} to {valid_dates.max()}")
                print(f"   Records with valid dates: {len(valid_dates):,} / {len(new_data):,}")
                
                # Check if data is chronologically ordered
                is_sorted = valid_dates.is_monotonic_increasing
                print(f"   Chronologically sorted: {is_sorted}")
                
                if not is_sorted:
                    print(f"   ⚠️ Data is NOT chronologically sorted!")
                    print(f"   This could affect position-based sync!")
        except Exception as e:
            print(f"   ❌ Error analyzing dates: {e}")
    
    # Summary and recommendations
    print(f"\n💡 Summary & Recommendations:")
    print(f"   📊 Your Excel file has 2,398 records")
    print(f"   📊 Your Airtable has 2,371 records")
    print(f"   📊 Missing from Airtable: {2398 - 2371} records")
    
    if len(old_data) != 2371:
        print(f"   ⚠️ Old file has {len(old_data):,} records, but Airtable has 2,371")
        print(f"   ⚠️ This suggests sync issues or data modifications")
    
    print(f"\n🚀 Recommended Actions:")
    print(f"   1. Use 'Smart Incremental' sync to add missing 27 records")
    print(f"   2. Verify data integrity with file comparison tool")
    print(f"   3. Check for duplicate Contact IDs before syncing")
    print(f"   4. Consider using 'Replace' mode for clean slate if needed")
    
    print(f"\n✅ Analysis complete!")

if __name__ == "__main__":
    analyze_ghl_files()
