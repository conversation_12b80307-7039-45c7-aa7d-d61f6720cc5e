#!/usr/bin/env python3
"""
Enhanced GHL Sync Test Script
Tests the enhanced Smart Incremental sync with detailed diagnostics
"""

import pandas as pd
import json
from GAdsEx_Modern import AirtableManager

def test_enhanced_ghl_sync():
    """Test the enhanced GHL sync with comprehensive diagnostics"""
    
    print("🔍 ENHANCED GHL SYNC DIAGNOSTIC TEST")
    print("=" * 60)
    
    # Load configuration
    try:
        with open('ads_extractor_config.json', 'r') as f:
            config = json.load(f)
        
        api_key = config.get('ghl_sync', {}).get('api_key', '')
        base_id = config.get('ghl_sync', {}).get('base_id', 'app7ffftdM6e3yekG')
        table_id = config.get('ghl_sync', {}).get('table_id', 'tblcdFVUC3zJrbmNf')
        
        if not api_key:
            print("❌ No Airtable API key found in configuration")
            return
            
        print(f"✅ Configuration loaded:")
        print(f"   - API Key: {api_key[:10]}...")
        print(f"   - Base ID: {base_id}")
        print(f"   - Table ID: {table_id}")
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return
    
    # Load GHL data
    try:
        print(f"\n📂 Loading GHL data...")
        ghl_data = pd.read_excel('quickfix - ghl.xlsx')
        print(f"✅ Loaded {len(ghl_data):,} records from Excel file")
        
        # Show data structure
        print(f"\n📊 Data Structure Analysis:")
        print(f"   - Columns: {list(ghl_data.columns)}")
        print(f"   - First record Contact ID: {ghl_data.iloc[0].get('Contact ID', 'N/A')}")
        print(f"   - Last record Contact ID: {ghl_data.iloc[-1].get('Contact ID', 'N/A')}")
        
        # Check for missing values in critical fields
        critical_fields = ['Contact ID', 'Opportunity ID', 'Date Created']
        for field in critical_fields:
            if field in ghl_data.columns:
                missing = ghl_data[field].isna().sum()
                print(f"   - {field} missing values: {missing:,}")
            else:
                print(f"   - ⚠️ {field} not found!")
                
    except Exception as e:
        print(f"❌ Error loading GHL data: {e}")
        return
    
    # Initialize Airtable manager
    try:
        print(f"\n🔗 Initializing Airtable connection...")
        manager = AirtableManager(api_key)
        manager.base_id = base_id
        manager.ghl_table_id = table_id
        
        # Test connection
        success, message = manager.test_connection()
        if not success:
            print(f"❌ Connection failed: {message}")
            return
        
        print(f"✅ Connection successful: {message}")
        
    except Exception as e:
        print(f"❌ Error initializing Airtable manager: {e}")
        return
    
    # Run enhanced sync test
    try:
        print(f"\n🚀 STARTING ENHANCED SMART INCREMENTAL SYNC TEST")
        print(f"=" * 60)
        
        # This will run the enhanced sync with detailed diagnostics
        uploaded_count, errors, skipped_count = manager.upload_ghl_data(
            ghl_data, 
            batch_size=10, 
            mode="smart_incremental"
        )
        
        print(f"\n📊 SYNC TEST RESULTS:")
        print(f"=" * 60)
        print(f"   - Records uploaded: {uploaded_count:,}")
        print(f"   - Records skipped: {skipped_count:,}")
        print(f"   - Errors: {len(errors) if isinstance(errors, list) else 0}")
        
        if errors and isinstance(errors, list):
            print(f"\n❌ Errors encountered:")
            for error in errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
        
        print(f"\n✅ Enhanced sync test completed!")
        print(f"📋 Check the detailed logs above to identify the sync discrepancy cause.")
        
    except Exception as e:
        print(f"❌ Error during sync test: {e}")
        import traceback
        traceback.print_exc()

def analyze_current_airtable_state():
    """Analyze current Airtable state for comparison"""
    
    print(f"\n🔍 ANALYZING CURRENT AIRTABLE STATE")
    print(f"=" * 60)
    
    try:
        with open('ads_extractor_config.json', 'r') as f:
            config = json.load(f)
        
        api_key = config.get('ghl_sync', {}).get('api_key', '')
        base_id = config.get('ghl_sync', {}).get('base_id', 'app7ffftdM6e3yekG')
        table_id = config.get('ghl_sync', {}).get('table_id', 'tblcdFVUC3zJrbmNf')
        
        manager = AirtableManager(api_key)
        manager.base_id = base_id
        manager.ghl_table_id = table_id
        
        # Get current Airtable state
        last_record, total_count = manager.get_last_record_from_airtable(table_id, 'Date Created')
        
        print(f"📊 Current Airtable State:")
        print(f"   - Total records: {total_count:,}")
        
        if last_record:
            print(f"   - Last record Contact ID: {last_record.get('Contact ID', 'N/A')}")
            print(f"   - Last record Opportunity ID: {last_record.get('Opportunity ID', 'N/A')}")
            print(f"   - Last record Date: {last_record.get('Date Created', 'N/A')}")
            print(f"   - Last record Name: {last_record.get('contact name', 'N/A')}")
        
        # Load local file for comparison
        ghl_data = pd.read_excel('quickfix - ghl.xlsx')
        print(f"\n📊 Local File State:")
        print(f"   - Total records: {len(ghl_data):,}")
        print(f"   - Expected missing: {len(ghl_data) - total_count:,}")
        
        if len(ghl_data) - total_count == 27:
            print(f"   ✅ Confirms 27-record discrepancy!")
        else:
            print(f"   ⚠️ Discrepancy is {len(ghl_data) - total_count:,} records, not 27")
            
    except Exception as e:
        print(f"❌ Error analyzing Airtable state: {e}")

if __name__ == "__main__":
    print("🚀 Enhanced GHL Sync Diagnostic Tool")
    print("=" * 60)
    
    # First analyze current state
    analyze_current_airtable_state()
    
    # Then run the enhanced sync test
    test_enhanced_ghl_sync()
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Review the detailed logs above")
    print(f"   2. Look for the exact position where matching failed")
    print(f"   3. Check if the last Airtable record exists in local file")
    print(f"   4. Verify data format consistency")
    print(f"   5. Run actual sync if diagnostics look good")
