# 📊 Google Sheets Integration Setup Guide

## 🎯 Current Status

✅ **Authentication Working**: Your Google Sheets credentials are valid  
✅ **Client ID Updated**: Application now uses your client ID (`************-oksa3m7juen98g8hbm3d0bbsiu2jd3e7.apps.googleusercontent.com`)  
✅ **Manual Entry Added**: New feature to manually add spreadsheets  
⚠️ **Drive API Issue**: Google Drive API needs to be enabled for automatic spreadsheet discovery  

---

## 🔧 **Option 1: Enable Google Drive API (Recommended)**

### **Step 1: Enable Drive API**
1. **Go to Google Cloud Console**: 
   ```
   https://console.developers.google.com/apis/api/drive.googleapis.com/overview?project=************
   ```

2. **Click "ENABLE"** to enable the Google Drive API

3. **Wait 2-3 minutes** for the changes to propagate

4. **Restart your application** and try "Refresh List" in the Google Sheets tab

### **Step 2: Test the Integration**
1. Open your application
2. Go to **Google Sheets** tab
3. Click **"Authenticate"** (should show ✅ Authenticated)
4. Click **"Refresh List"** - your spreadsheets should now appear automatically

---

## 🔧 **Option 2: Use Manual Entry (Works Now)**

If you prefer not to enable the Drive API or want to add specific spreadsheets:

### **Step 1: Get Spreadsheet ID**
1. Open any Google Spreadsheet in your browser
2. Copy the ID from the URL:
   ```
   https://docs.google.com/spreadsheets/d/[SPREADSHEET_ID]/edit
   ```
   Example: `1oN6s0XtE3fj3lzpUNBocj0CQMkBYyJXmoEtbuSsJd3o`

### **Step 2: Add Manually in Application**
1. Open your application
2. Go to **Google Sheets** tab
3. Click **"Authenticate"** (should show ✅ Authenticated)
4. Click **"➕ Add Manual Entry"**
5. Enter the spreadsheet ID or full URL
6. Give it a display name
7. Click **"🧪 Test & Add"**

---

## 🧪 **Testing Your Setup**

### **Test 1: Authentication**
```bash
python test_google_sheets_auth.py
```

**Expected Result**: 
- ✅ Token loaded successfully
- ✅ Existing credentials are valid
- ✅ Sheets service created successfully
- Either: ✅ Found X spreadsheets OR ❌ Drive API error (which is expected)

### **Test 2: Manual Entry**
1. Open application → Google Sheets tab
2. Click "Authenticate" → Should show ✅ Authenticated
3. Click "Add Manual Entry"
4. Enter a spreadsheet ID you have access to
5. Should successfully add and appear in the list

---

## 📋 **Current Configuration**

### **Your Credentials**
- **Client ID**: `************-oksa3m7juen98g8hbm3d0bbsiu2jd3e7.apps.googleusercontent.com`
- **Project ID**: `ghl-sheets`
- **Token File**: `sheets_token.json` ✅ Valid until 2025-07-03 21:11:20
- **Credentials File**: `user.json` ✅ Properly formatted

### **API Status**
- **Google Sheets API**: ✅ Working
- **Google Drive API**: ❌ Not enabled (needed for auto-discovery)

---

## 🔍 **Troubleshooting**

### **Problem**: "Drive API not enabled" error
**Solution**: Follow Option 1 above to enable the Drive API

### **Problem**: "Permission denied" when adding spreadsheet
**Solution**: 
1. Make sure the spreadsheet is shared with your Google account
2. Check that you're using the correct spreadsheet ID
3. Verify you have at least "Viewer" access to the spreadsheet

### **Problem**: "Not authenticated" error
**Solution**:
1. Delete `sheets_token.json` file
2. Restart the application
3. Click "Authenticate" and complete the OAuth flow

### **Problem**: Can't see spreadsheets in the list
**Solution**:
1. If Drive API is enabled: Click "Refresh List"
2. If Drive API is not enabled: Use "Add Manual Entry" for each spreadsheet

---

## 🎯 **Next Steps**

### **Immediate Actions**
1. **Choose your approach**:
   - **Option A**: Enable Drive API for automatic discovery
   - **Option B**: Use manual entry for specific spreadsheets

2. **Test the integration**:
   - Open application → Google Sheets tab
   - Verify authentication status
   - Try adding a spreadsheet (manually or automatically)

3. **Start using the features**:
   - Select spreadsheets from the list
   - Choose worksheets
   - Preview data
   - Import/export functionality (coming soon)

### **Advanced Usage**
- **Bulk Import**: Add multiple spreadsheets manually
- **Saved Spreadsheets**: Your manually added spreadsheets are saved in config
- **Client Integration**: Spreadsheet data can be organized by client

---

## 📞 **Support Information**

### **Files Created/Modified**
- ✅ `GAdsEx_Modern.py` - Updated with your client ID and manual entry feature
- ✅ `test_google_sheets_auth.py` - Authentication testing script
- ✅ `sheets_token.json` - Your valid authentication token
- ✅ `user.json` - Your OAuth credentials

### **Key Features Added**
- ✅ **Manual Spreadsheet Entry**: Add spreadsheets by ID/URL
- ✅ **Graceful Drive API Handling**: Works with or without Drive API
- ✅ **Saved Spreadsheets**: Manual entries are saved in configuration
- ✅ **Enhanced Error Messages**: Clear guidance for common issues

---

## 🎉 **Success Indicators**

You'll know everything is working when:
- ✅ Google Sheets tab shows "✅ Authenticated"
- ✅ You can add spreadsheets (manually or automatically)
- ✅ Spreadsheets appear in the list with correct names
- ✅ You can select spreadsheets and see worksheet options
- ✅ No error messages in the application logs

**Your Google Sheets integration is ready to use!** 🚀
