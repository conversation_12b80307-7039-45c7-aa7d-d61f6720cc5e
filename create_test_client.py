#!/usr/bin/env python3
"""
Create a test client folder to verify auto-detection functionality
"""

import os

def create_test_client():
    """Create a test client folder structure"""
    print('🧪 Testing Client Management System...')
    
    # Create a test client folder to verify auto-detection
    clients_dir = 'clients'
    test_client = 'TestClient_AutoDetect'
    test_client_path = os.path.join(clients_dir, test_client)
    
    if not os.path.exists(test_client_path):
        os.makedirs(test_client_path, exist_ok=True)
        os.makedirs(os.path.join(test_client_path, 'exports'), exist_ok=True)
        os.makedirs(os.path.join(test_client_path, 'data'), exist_ok=True)
        os.makedirs(os.path.join(test_client_path, 'reports'), exist_ok=True)
        os.makedirs(os.path.join(test_client_path, 'temp'), exist_ok=True)
        
        # Create a test file
        with open(os.path.join(test_client_path, 'exports', 'test_file.csv'), 'w') as f:
            f.write('Date,Campaign,Cost\n2024-01-01,Test Campaign,100.00\n')
        
        print(f'✅ Created test client folder: {test_client_path}')
        print(f'📁 Folder structure created with subfolders')
        print(f'📄 Test file created in exports folder')
        print(f'🔍 This client should be auto-detected when you restart the app')
    else:
        print(f'ℹ️ Test client folder already exists: {test_client_path}')
    
    print('✅ Client management system is ready for testing!')

if __name__ == "__main__":
    create_test_client()
