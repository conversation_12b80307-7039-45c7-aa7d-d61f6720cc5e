#!/usr/bin/env python3
"""
Test Google Sheets authentication and list available spreadsheets
"""

import os
import json
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build

# Google Sheets specific scopes
SHEETS_SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/drive.readonly'
]

def test_google_sheets_authentication():
    """Test Google Sheets authentication and list spreadsheets"""
    print("🧪 Testing Google Sheets Authentication")
    print("=" * 50)
    
    token_file = "sheets_token.json"
    credentials_file = "user.json"
    
    # Check if files exist
    print(f"📄 Checking for token file: {token_file}")
    if os.path.exists(token_file):
        print(f"   ✅ Found: {token_file}")
    else:
        print(f"   ❌ Missing: {token_file}")
    
    print(f"📄 Checking for credentials file: {credentials_file}")
    if os.path.exists(credentials_file):
        print(f"   ✅ Found: {credentials_file}")
    else:
        print(f"   ❌ Missing: {credentials_file}")
        return False
    
    # Load and check credentials
    try:
        with open(credentials_file, 'r') as f:
            cred_data = json.load(f)
        
        if 'installed' in cred_data:
            client_id = cred_data['installed']['client_id']
            print(f"📋 Client ID: {client_id}")
        else:
            print("❌ Invalid credentials file format")
            return False
            
    except Exception as e:
        print(f"❌ Error reading credentials file: {e}")
        return False
    
    # Test authentication
    creds = None
    
    # Load existing token
    if os.path.exists(token_file):
        try:
            print("🔑 Loading existing token...")
            creds = Credentials.from_authorized_user_file(token_file, SHEETS_SCOPES)
            print("   ✅ Token loaded successfully")
        except Exception as e:
            print(f"   ❌ Error loading token: {e}")
    
    # Check if credentials are valid
    if creds and creds.valid:
        print("✅ Existing credentials are valid")
    elif creds and creds.expired and creds.refresh_token:
        print("🔄 Refreshing expired token...")
        try:
            creds.refresh(Request())
            print("   ✅ Token refreshed successfully")
            
            # Save refreshed token
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
            print("   💾 Refreshed token saved")
            
        except Exception as e:
            print(f"   ❌ Error refreshing token: {e}")
            creds = None
    else:
        print("🔐 Need to authenticate...")
        creds = None
    
    # If no valid credentials, run OAuth flow
    if not creds:
        try:
            print("🌐 Starting OAuth flow...")
            flow = InstalledAppFlow.from_client_secrets_file(
                credentials_file, SHEETS_SCOPES
            )
            creds = flow.run_local_server(port=0)
            print("   ✅ OAuth flow completed successfully")
            
            # Save credentials for next run
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
            print("   💾 New token saved")
            
        except Exception as e:
            print(f"   ❌ OAuth flow failed: {e}")
            return False
    
    # Test Google Sheets API
    try:
        print("📊 Testing Google Sheets API...")
        service = build('sheets', 'v4', credentials=creds)
        print("   ✅ Sheets service created successfully")
        
        print("📁 Testing Google Drive API...")
        drive_service = build('drive', 'v3', credentials=creds)
        print("   ✅ Drive service created successfully")
        
        # Get list of spreadsheets
        print("📋 Fetching spreadsheets...")
        results = drive_service.files().list(
            q="mimeType='application/vnd.google-apps.spreadsheet'",
            pageSize=20,
            fields="nextPageToken, files(id, name, modifiedTime, owners)"
        ).execute()
        
        items = results.get('files', [])
        
        if not items:
            print("   ⚠️ No spreadsheets found")
        else:
            print(f"   ✅ Found {len(items)} spreadsheets:")
            for i, item in enumerate(items, 1):
                name = item['name']
                sheet_id = item['id']
                modified = item.get('modifiedTime', 'Unknown')
                owners = item.get('owners', [])
                owner_name = owners[0]['displayName'] if owners else 'Unknown'
                
                print(f"      {i}. {name}")
                print(f"         ID: {sheet_id}")
                print(f"         Modified: {modified}")
                print(f"         Owner: {owner_name}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def check_token_details():
    """Check details of the current token"""
    print("\n🔍 Token Details Analysis")
    print("=" * 50)
    
    token_file = "sheets_token.json"
    
    if not os.path.exists(token_file):
        print(f"❌ Token file not found: {token_file}")
        return
    
    try:
        with open(token_file, 'r') as f:
            token_data = json.load(f)
        
        print("📋 Token Information:")
        print(f"   Client ID: {token_data.get('client_id', 'Not found')}")
        print(f"   Scopes: {token_data.get('scopes', 'Not found')}")
        print(f"   Expiry: {token_data.get('expiry', 'Not found')}")
        print(f"   Has Refresh Token: {'Yes' if token_data.get('refresh_token') else 'No'}")
        
        # Check if token is expired
        if 'expiry' in token_data:
            from datetime import datetime
            try:
                expiry_time = datetime.fromisoformat(token_data['expiry'].replace('Z', '+00:00'))
                current_time = datetime.now(expiry_time.tzinfo)
                
                if current_time > expiry_time:
                    print("   ⚠️ Token is EXPIRED")
                else:
                    time_left = expiry_time - current_time
                    print(f"   ✅ Token valid for: {time_left}")
            except Exception as e:
                print(f"   ❌ Error parsing expiry time: {e}")
        
    except Exception as e:
        print(f"❌ Error reading token file: {e}")

if __name__ == "__main__":
    print("🚀 Google Sheets Authentication Test")
    print("=" * 60)
    
    # Check token details first
    check_token_details()
    
    # Test authentication
    success = test_google_sheets_authentication()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("✅ Google Sheets authentication is working correctly")
        print("✅ You should now be able to see your spreadsheets in the app")
        print("\nNext steps:")
        print("1. Restart your main application")
        print("2. Go to the Google Sheets tab")
        print("3. Click 'Authenticate' or 'Refresh Spreadsheets'")
        print("4. Your spreadsheets should now appear in the list")
    else:
        print("\n❌ FAILED!")
        print("Google Sheets authentication needs to be set up")
        print("\nTroubleshooting steps:")
        print("1. Make sure you have the correct credentials file (user.json)")
        print("2. Check that your Google Cloud project has the Sheets API enabled")
        print("3. Verify the redirect URIs in your OAuth client configuration")
        print("4. Try deleting sheets_token.json and re-authenticating")
